#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON数据转Excel工具
将JSON格式的数据解析并写入Excel表格中
"""

import json
import pandas as pd
from typing import Dict, List, Any
import os


class JsonToExcelConverter:
    """JSON到Excel转换器类"""

    def __init__(self):
        """初始化转换器"""
        self.data = None
        self.df = None

    def load_json_file(self, file_path: str) -> bool:
        """
        从文件加载JSON数据

        Args:
            file_path: JSON文件路径

        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                self.data = json.load(file)
            print(f"成功加载JSON文件: {file_path}")
            return True
        except FileNotFoundError:
            print(f"错误: 找不到文件 {file_path}")
            return False
        except json.JSONDecodeError as e:
            print(f"错误: JSON格式不正确 - {e}")
            return False
        except Exception as e:
            print(f"错误: 加载文件时发生异常 - {e}")
            return False

    def load_json_string(self, json_string: str) -> bool:
        """
        从字符串加载JSON数据

        Args:
            json_string: JSON字符串

        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            self.data = json.loads(json_string)
            print("成功加载JSON字符串")
            return True
        except json.JSONDecodeError as e:
            print(f"错误: JSON格式不正确 - {e}")
            return False
        except Exception as e:
            print(f"错误: 解析JSON字符串时发生异常 - {e}")
            return False

    def parse_items_to_dataframe(self) -> bool:
        """
        将JSON中的items数组解析为DataFrame

        Returns:
            bool: 解析成功返回True，失败返回False
        """
        if not self.data:
            print("错误: 没有加载JSON数据")
            return False

        if 'items' not in self.data:
            print("错误: JSON数据中没有找到'items'字段")
            return False

        items = self.data['items']
        if not isinstance(items, list):
            print("错误: 'items'字段不是数组格式")
            return False

        if not items:
            print("警告: 'items'数组为空")
            self.df = pd.DataFrame()
            return True

        try:
            # 将items列表转换为DataFrame
            self.df = pd.DataFrame(items)
            print(f"成功解析 {len(items)} 条记录")
            print(f"字段列表: {list(self.df.columns)}")
            return True
        except Exception as e:
            print(f"错误: 解析items数据时发生异常 - {e}")
            return False

    def save_to_excel(self, output_path: str, sheet_name: str = 'Sheet1') -> bool:
        """
        将DataFrame保存为Excel文件

        Args:
            output_path: 输出Excel文件路径
            sheet_name: 工作表名称，默认为'Sheet1'

        Returns:
            bool: 保存成功返回True，失败返回False
        """
        if self.df is None:
            print("错误: 没有可保存的数据，请先解析JSON")
            return False

        try:
            # 创建输出目录（如果不存在）
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 保存到Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                self.df.to_excel(writer, sheet_name=sheet_name, index=False)

            print(f"成功保存Excel文件: {output_path}")
            print(f"工作表名称: {sheet_name}")
            print(f"数据行数: {len(self.df)}")
            print(f"数据列数: {len(self.df.columns)}")
            return True
        except Exception as e:
            print(f"错误: 保存Excel文件时发生异常 - {e}")
            return False

    def display_preview(self, rows: int = 5) -> None:
        """
        显示数据预览

        Args:
            rows: 显示的行数，默认为5行
        """
        if self.df is None:
            print("没有可预览的数据")
            return

        print(f"\n数据预览 (前{min(rows, len(self.df))}行):")
        print("=" * 80)
        print(self.df.head(rows).to_string(index=False))
        print("=" * 80)
        print(f"总计: {len(self.df)} 行, {len(self.df.columns)} 列")


def main():
    """主函数 - 演示如何使用转换器"""
    # 示例JSON数据
    sample_json = {
        "items": [
            {
                "complaint_proj_id": "d999c73c-6e95-e811-80bf-0a94ef2f6b45",
                "complaint_proj_name": "重庆洺悦城（华西区域）",
                "code": "P00000002",
                "is_end": "0",
                "parent_code": "",
                "corp_id": "5b2bc65d-b358-ea11-80bf-0a94ef2f6b45",
                "corp_name": "华西区域"
            },
            {
                "complaint_proj_id": "72aeda18-6f95-e811-80bf-0a94ef2f6b45",
                "complaint_proj_name": "重庆洺悦城-洺悦城．龙洲湾（华西区域）",
                "code": "P00000002.P00000001",
                "is_end": "1",
                "parent_code": "P00000002",
                "corp_id": "5b2bc65d-b358-ea11-80bf-0a94ef2f6b45",
                "corp_name": "华西区域"
            }
        ]
    }

    # 创建转换器实例
    converter = JsonToExcelConverter()

    # 方式1: 从JSON字符串加载数据
    print("方式1: 从JSON字符串加载数据")
    if converter.load_json_string(json.dumps(sample_json, ensure_ascii=False)):
        if converter.parse_items_to_dataframe():
            converter.display_preview()
            converter.save_to_excel('output_from_string.xlsx', '项目数据')

    print("\n" + "="*60 + "\n")

    # 方式2: 从JSON文件加载数据（如果文件存在）
    json_file_path = 'sample_data.json'
    print(f"方式2: 从JSON文件加载数据 ({json_file_path})")

    # 创建示例JSON文件
    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump(sample_json, f, ensure_ascii=False, indent=2)
    print(f"已创建示例JSON文件: {json_file_path}")

    # 重新创建转换器实例
    converter2 = JsonToExcelConverter()
    if converter2.load_json_file(json_file_path):
        if converter2.parse_items_to_dataframe():
            converter2.display_preview()
            converter2.save_to_excel('output_from_file.xlsx', '项目数据')


if __name__ == "__main__":
    main()