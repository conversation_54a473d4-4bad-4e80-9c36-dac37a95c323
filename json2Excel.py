#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON数据转Excel工具
将JSON格式的数据解析并写入Excel表格中
"""

import json
import pandas as pd
from typing import Dict, List, Any
import os


class JsonToExcelConverter:
    """JSON到Excel转换器类"""

    def __init__(self):
        """初始化转换器"""
        self.data = None
        self.df = None

    def _fix_json_format(self, content: str) -> str:
        """
        修复常见的JSON格式问题

        Args:
            content: 原始JSON字符串

        Returns:
            str: 修复后的JSON字符串
        """
        import re

        # 修复数组末尾多余的逗号 (如: }, ],)
        content = re.sub(r'},\s*]', '}]', content)

        # 修复对象末尾多余的逗号 (如: "key": "value",})
        content = re.sub(r',\s*}', '}', content)

        return content

    def load_json_file(self, file_path: str) -> bool:
        """
        从文件加载JSON数据，自动检测编码格式

        Args:
            file_path: JSON文件路径

        Returns:
            bool: 加载成功返回True，失败返回False
        """
        # 尝试不同的编码格式
        encodings = ['utf-8', 'utf-8-sig', 'utf-32', 'utf-32-le', 'utf-16', 'utf-16-le', 'gbk', 'gb2312', 'latin-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    content = file.read()
                    # 清理可能的BOM和其他特殊字符
                    content = content.strip()
                    if content.startswith('\ufeff'):
                        content = content[1:]

                    # 修复常见的JSON格式问题
                    content = self._fix_json_format(content)

                    self.data = json.loads(content)
                print(f"成功加载JSON文件: {file_path} (编码: {encoding})")
                return True
            except UnicodeDecodeError:
                continue
            except json.JSONDecodeError as e:
                print(f"JSON格式错误 (编码: {encoding}): {e}")
                continue
            except FileNotFoundError:
                print(f"错误: 找不到文件 {file_path}")
                return False
            except Exception as e:
                print(f"尝试编码 {encoding} 时发生异常: {e}")
                continue

        print(f"错误: 无法使用任何编码格式读取文件 {file_path}")
        return False

    def load_json_string(self, json_string: str) -> bool:
        """
        从字符串加载JSON数据

        Args:
            json_string: JSON字符串

        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            self.data = json.loads(json_string)
            print("成功加载JSON字符串")
            return True
        except json.JSONDecodeError as e:
            print(f"错误: JSON格式不正确 - {e}")
            return False
        except Exception as e:
            print(f"错误: 解析JSON字符串时发生异常 - {e}")
            return False

    def parse_items_to_dataframe(self) -> bool:
        """
        将JSON中的items数组解析为DataFrame

        Returns:
            bool: 解析成功返回True，失败返回False
        """
        if not self.data:
            print("错误: 没有加载JSON数据")
            return False

        if 'items' not in self.data:
            print("错误: JSON数据中没有找到'items'字段")
            return False

        items = self.data['items']
        if not isinstance(items, list):
            print("错误: 'items'字段不是数组格式")
            return False

        if not items:
            print("警告: 'items'数组为空")
            self.df = pd.DataFrame()
            return True

        try:
            # 将items列表转换为DataFrame
            self.df = pd.DataFrame(items)
            print(f"成功解析 {len(items)} 条记录")
            print(f"字段列表: {list(self.df.columns)}")
            return True
        except Exception as e:
            print(f"错误: 解析items数据时发生异常 - {e}")
            return False

    def save_to_excel(self, output_path: str, sheet_name: str = 'Sheet1') -> bool:
        """
        将DataFrame保存为Excel文件

        Args:
            output_path: 输出Excel文件路径
            sheet_name: 工作表名称，默认为'Sheet1'

        Returns:
            bool: 保存成功返回True，失败返回False
        """
        if self.df is None:
            print("错误: 没有可保存的数据，请先解析JSON")
            return False

        try:
            # 创建输出目录（如果不存在）
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 保存到Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                self.df.to_excel(writer, sheet_name=sheet_name, index=False)

            print(f"成功保存Excel文件: {output_path}")
            print(f"工作表名称: {sheet_name}")
            print(f"数据行数: {len(self.df)}")
            print(f"数据列数: {len(self.df.columns)}")
            return True
        except Exception as e:
            print(f"错误: 保存Excel文件时发生异常 - {e}")
            return False

    def display_preview(self, rows: int = 5) -> None:
        """
        显示数据预览

        Args:
            rows: 显示的行数，默认为5行
        """
        if self.df is None:
            print("没有可预览的数据")
            return

        print(f"\n数据预览 (前{min(rows, len(self.df))}行):")
        print("=" * 80)
        print(self.df.head(rows).to_string(index=False))
        print("=" * 80)
        print(f"总计: {len(self.df)} 行, {len(self.df.columns)} 列")


def process_project_json():
    """处理project.json文件并转换为Excel"""
    json_file_path = 'project.json'
    output_excel_path = 'project_data.xlsx'

    print("="*80)
    print("JSON转Excel处理程序")
    print("="*80)

    # 检查文件是否存在
    if not os.path.exists(json_file_path):
        print(f"错误: 找不到文件 {json_file_path}")
        print("请确保project.json文件存在于当前目录中")
        return False

    # 创建转换器实例
    converter = JsonToExcelConverter()

    # 加载JSON文件
    print(f"正在读取文件: {json_file_path}")
    if not converter.load_json_file(json_file_path):
        return False

    # 解析数据
    print("正在解析JSON数据...")
    if not converter.parse_items_to_dataframe():
        return False

    # 显示数据预览
    converter.display_preview(rows=10)

    # 显示字段详细信息
    print("\n字段详细信息:")
    print("-" * 60)
    if converter.df is not None:
        for i, column in enumerate(converter.df.columns, 1):
            print(f"{i:2d}. {column}")
            # 显示该字段的前几个值作为示例
            sample_values = converter.df[column].dropna().head(3).tolist()
            if sample_values:
                print(f"    示例值: {sample_values}")
            print()

    # 保存到Excel文件
    print(f"正在保存到Excel文件: {output_excel_path}")
    if converter.save_to_excel(output_excel_path, '项目数据'):
        print(f"\n✅ 成功完成转换!")
        print(f"📁 输出文件: {output_excel_path}")
        print(f"📊 数据统计: {len(converter.df)} 行 × {len(converter.df.columns)} 列")
        return True
    else:
        print("❌ 保存Excel文件失败")
        return False


def main():
    """主函数"""
    try:
        process_project_json()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()