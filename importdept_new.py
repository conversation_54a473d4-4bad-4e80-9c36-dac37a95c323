#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部门数据导入脚本 - 基于full_path层级结构
根据organizations.xlsx中的full_path列（以>分隔的层级）自动构建完整的部门层级关系
"""

import pandas as pd
import uuid
from datetime import datetime


def generate_insert_script(excel_file_path, table_name='sys_dept'):
    """
    根据Excel文件的full_path层级结构生成部门数据的INSERT脚本
    
    Args:
        excel_file_path (str): Excel文件路径
        table_name (str): 目标表名
    
    Returns:
        str: 生成的SQL脚本内容
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file_path, header=0)
        
        if df.empty:
            print("Excel文件为空或无法读取")
            return ""
        
        print(f"成功读取Excel文件，共 {len(df)} 行数据")
        
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")
        return ""

    # 用于存储所有部门的映射关系: {full_path: {'id': ..., 'code': ..., 'name': ..., 'grade': ...}}
    dept_map = {}
    # 用于存储每个父部门下的子部门序号: {父部门code: 序号}
    child_counter = {}
    # 存储所有生成的SQL语句
    sql_statements = []
    
    # 收集所有需要处理的路径（包括中间层级）
    all_paths = set()
    path_info = {}  # 存储路径的详细信息
    
    print("分析Excel数据，提取所有层级路径...")
    
    # 从Excel数据中提取所有路径信息
    for index, row in df.iterrows():
        full_path = str(row['full_path']).strip()
        name = str(row['name']).strip()
        grade = int(row['grade'])
        
        # 记录当前路径的信息
        path_info[full_path] = {
            'name': name,
            'grade': grade,
            'is_from_excel': True  # 标记这是Excel中的原始数据
        }
        all_paths.add(full_path)
        
        # 提取所有中间层级路径
        path_parts = full_path.split('>')
        for i in range(1, len(path_parts)):
            intermediate_path = '>'.join(path_parts[:i+1])
            
            # 如果中间路径不存在，创建它
            if intermediate_path not in path_info:
                path_info[intermediate_path] = {
                    'name': path_parts[i].strip(),
                    'grade': i + 1,  # 基于层级深度推断grade
                    'is_from_excel': False  # 标记这是自动生成的中间层级
                }
                all_paths.add(intermediate_path)
    
    print(f"识别出 {len(all_paths)} 个唯一路径（包括自动生成的中间层级）")
    
    # 按路径长度排序，确保父部门先于子部门处理
    sorted_paths = sorted(all_paths, key=lambda x: (len(x.split('>')), x))
    
    print("开始生成SQL语句...")
    
    # 处理每个路径
    for full_path in sorted_paths:
        path_parts = full_path.split('>')
        current_name = path_info[full_path]['name']
        grade = path_info[full_path]['grade']
        is_from_excel = path_info[full_path]['is_from_excel']
        
        # 生成唯一ID
        current_id = str(uuid.uuid4())
        
        # 确定父部门路径和code
        if len(path_parts) == 1:
            # 根节点
            parent_code = None
            current_code = 'DJDC'
        else:
            # 子部门
            parent_path = '>'.join(path_parts[:-1])
            parent_info = dept_map.get(parent_path)
            
            if not parent_info:
                print(f"错误: 找不到父部门 '{parent_path}' for '{full_path}'")
                continue
            
            parent_code = parent_info['code']
            
            # 生成子部门code
            if parent_code not in child_counter:
                child_counter[parent_code] = 1
            else:
                child_counter[parent_code] += 1
            
            # 将序号格式化为两位数
            child_num = str(child_counter[parent_code]).zfill(2)
            current_code = f"{parent_code}{child_num}"
        
        # 存储部门信息
        dept_map[full_path] = {
            'id': current_id,
            'code': current_code,
            'name': current_name,
            'grade': grade
        }
        
        # 生成SQL语句
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        pid_value = f"'{parent_code}'" if parent_code else 'NULL'
        
        sql = (
            f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, `type`, `source`, `status`, `sort`, `createTime`, `createPeople`, `bpmStatus`, `orgType`, `orderNo`, `grade`, `updateTime`, `updatePeople`) VALUES "
            f"('{current_id}', {pid_value}, '{current_name}', '{current_code}', NULL, NULL, NULL, 0, 1, '{now}', '系统导入', NULL, NULL, NULL, {grade}, NULL, NULL);"
        )
        sql_statements.append(sql)
        
        # 输出处理信息
        status = "Excel数据" if is_from_excel else "自动生成"
        print(f"处理 [{status}]: {full_path} -> {current_code} (grade: {grade})")
    
    print(f"\n生成完成！总共生成了 {len(sql_statements)} 条SQL语句")
    
    # 统计各级部门数量
    grade_counts = {}
    excel_count = 0
    auto_count = 0
    
    for path, info in path_info.items():
        grade = info['grade']
        grade_counts[grade] = grade_counts.get(grade, 0) + 1
        if info['is_from_excel']:
            excel_count += 1
        else:
            auto_count += 1
    
    print(f"Excel原始数据: {excel_count} 条")
    print(f"自动生成中间层级: {auto_count} 条")
    print("各级部门分布:")
    for grade in sorted(grade_counts.keys()):
        print(f"  {grade}级部门: {grade_counts[grade]} 个")
    
    return '\n'.join(sql_statements)


def main():
    """
    主函数
    """
    excel_file = 'organizations.xlsx'
    
    print("=" * 60)
    print("部门数据导入脚本 - 基于full_path层级结构")
    print("=" * 60)
    
    # 生成SQL脚本
    sql_content = generate_insert_script(excel_file)
    
    if sql_content:
        # 写入SQL文件
        output_file = 'insert_data.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        print(f"\n✅ 成功生成SQL脚本文件: {output_file}")
        print("您现在可以使用此文件将数据导入到MySQL中。")
        print(f"执行命令: mysql -u用户名 -p数据库名 < {output_file}")
    else:
        print("❌ 未能生成SQL内容，请检查Excel文件是否存在且格式正确。")


if __name__ == '__main__':
    main()
