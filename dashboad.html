<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts中国地图区域划分</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a3a 0%, #0a1a2a 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: #ecf0f1;
        }
        
        .container {
            max-width: 1200px;
            width: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            width: 100%;
        }
        
        h1 {
            font-size: 2.8rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #3498db, #2ecc71, #e74c3c);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .description {
            font-size: 1.1rem;
            color: #bdc3c7;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .map-container {
            width: 100%;
            height: 600px;
            background: rgba(30, 40, 50, 0.8);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        #chinaMap {
            width: 100%;
            height: 100%;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .control-btn {
            padding: 12px 20px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .region-legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            max-width: 800px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            padding: 8px 15px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        
        footer {
            margin-top: 30px;
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9rem;
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            width: 100%;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2.2rem;
            }
            
            .map-container {
                height: 400px;
            }
            
            .controls {
                flex-direction: column;
                width: 100%;
            }
            
            .control-btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>ECharts中国地图区域划分</h1>
            <p class="description">
                使用ECharts实现的中国地图区域划分展示，鼠标悬停可查看区域信息，点击按钮可切换显示模式。
            </p>
        </header>
        
        <div class="controls">
            <button class="control-btn" id="resetView">重置视图</button>
            <button class="control-btn" id="toggleLabels">切换标签显示</button>
            <button class="control-btn" id="zoomIn">放大</button>
            <button class="control-btn" id="zoomOut">缩小</button>
        </div>
        
        <div class="region-legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e74c3c;"></div>
                <span>东北地区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #3498db;"></div>
                <span>华北地区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #2ecc71;"></div>
                <span>华东地区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f1c40f;"></div>
                <span>华中地区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #9b59b6;"></div>
                <span>华南地区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #1abc9c;"></div>
                <span>西南地区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e67e22;"></div>
                <span>西北地区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e84393;"></div>
                <span>港澳台地区</span>
            </div>
        </div>
        
        <div class="map-container">
            <div id="chinaMap"></div>
        </div>
        
        <footer>
            <p>© 2023 ECharts中国地图区域展示 | 数据仅供参考</p>
        </footer>
    </div>

    <script>
        // 初始化ECharts实例
        const chartDom = document.getElementById('chinaMap');
        const myChart = echarts.init(chartDom);
        
        // 省份数据及其区域划分
        const provinceData = [
            {name: '黑龙江省', value: 100, region: 'northeast'},
            {name: '吉林省', value: 100, region: 'northeast'},
            {name: '辽宁省', value: 100, region: 'northeast'},
            {name: '内蒙古自治区', value: 100, region: 'north'},
            {name: '河北省', value: 100, region: 'north'},
            {name: '山西省', value: 100, region: 'north'},
            {name: '北京市', value: 100, region: 'north'},
            {name: '天津市', value: 100, region: 'north'},
            {name: '山东省', value: 100, region: 'east'},
            {name: '江苏省', value: 100, region: 'east'},
            {name: '浙江省', value: 100, region: 'east'},
            {name: '安徽省', value: 100, region: 'east'},
            {name: '上海市', value: 100, region: 'east'},
            {name: '福建省', value: 100, region: 'east'},
            {name: '江西省', value: 100, region: 'east'},
            {name: '河南省', value: 100, region: 'central'},
            {name: '湖北省', value: 100, region: 'central'},
            {name: '湖南省', value: 100, region: 'central'},
            {name: '广东省', value: 100, region: 'south'},
            {name: '广西壮族自治区', value: 100, region: 'south'},
            {name: '海南省', value: 100, region: 'south'},
            {name: '四川省', value: 100, region: 'southwest'},
            {name: '重庆市', value: 100, region: 'southwest'},
            {name: '贵州省', value: 100, region: 'southwest'},
            {name: '云南省', value: 100, region: 'southwest'},
            {name: '西藏自治区', value: 100, region: 'southwest'},
            {name: '陕西省', value: 100, region: 'northwest'},
            {name: '甘肃省', value: 100, region: 'northwest'},
            {name: '青海省', value: 100, region: 'northwest'},
            {name: '宁夏回族自治区', value: 100, region: 'northwest'},
            {name: '新疆维吾尔自治区', value: 100, region: 'northwest'},
            {name: '台湾省', value: 100, region: 'special'},
            {name: '香港特别行政区', value: 100, region: 'special'},
            {name: '澳门特别行政区', value: 100, region: 'special'}
        ];
        
        // 区域颜色映射
        const regionColors = {
            'northeast': '#e74c3c',
            'north': '#3498db',
            'east': '#2ecc71',
            'central': '#f1c40f',
            'south': '#9b59b6',
            'southwest': '#1abc9c',
            'northwest': '#e67e22',
            'special': '#e84393'
        };
        
        // 区域名称映射
        const regionNames = {
            'northeast': '东北地区',
            'north': '华北地区',
            'east': '华东地区',
            'central': '华中地区',
            'south': '华南地区',
            'southwest': '西南地区',
            'northwest': '西北地区',
            'special': '港澳台地区'
        };
        
        // 配置项
        let option = {
            backgroundColor: 'transparent',
            title: {
                text: '中国行政区划',
                subtext: '8大区域划分',
                left: 'center',
                textStyle: {
                    color: '#ecf0f1'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    const region = params.data.region;
                    return `${params.name}<br/>${regionNames[region]}`;
                }
            },
            visualMap: {
                show: false,
                pieces: [
                    {min: 1, max: 100, color: regionColors.northeast},
                    {min: 101, max: 200, color: regionColors.north},
                    {min: 201, max: 300, color: regionColors.east},
                    {min: 301, max: 400, color: regionColors.central},
                    {min: 401, max: 500, color: regionColors.south},
                    {min: 501, max: 600, color: regionColors.southwest},
                    {min: 601, max: 700, color: regionColors.northwest},
                    {min: 701, max: 800, color: regionColors.special}
                ]
            },
            series: [
                {
                    name: '中国',
                    type: 'map',
                    map: 'china',
                    roam: true,
                    emphasis: {
                        label: {
                            show: true,
                            color: '#fff',
                            fontSize: 14,
                            fontWeight: 'bold'
                        },
                        itemStyle: {
                            areaColor: '#ff7f50',
                            borderColor: '#fff',
                            borderWidth: 1
                        }
                    },
                    data: provinceData,
                    itemStyle: {
                        areaColor: function(params) {
                            return regionColors[params.data.region];
                        },
                        borderColor: '#0a1a2a',
                        borderWidth: 1
                    },
                    select: {
                        itemStyle: {
                            areaColor: '#ff7f50'
                        }
                    }
                }
            ]
        };
        
        // 使用配置项生成图表
        myChart.setOption(option);
        
        // 添加点击事件，实现区域高亮
        myChart.on('click', function(params) {
            // 获取被点击省份的区域
            const region = params.data.region;
            
            // 高亮同一区域的所有省份
            const highlightData = provinceData.map(item => {
                return {
                    ...item,
                    selected: item.region === region
                };
            });
            
            myChart.setOption({
                series: [{
                    data: highlightData
                }]
            });
        });
        
        // 添加鼠标悬停事件，显示区域信息
        myChart.on('mouseover', function(params) {
            const region = params.data.region;
            
            // 高亮同一区域的所有省份
            const highlightData = provinceData.map(item => {
                if (item.region === region) {
                    return {
                        ...item,
                        itemStyle: {
                            areaColor: '#ff7f50',
                            borderColor: '#fff',
                            borderWidth: 2
                        }
                    };
                }
                return item;
            });
            
            myChart.setOption({
                series: [{
                    data: highlightData
                }]
            });
        });
        
        // 鼠标移出时恢复原状
        myChart.on('mouseout', function() {
            myChart.setOption({
                series: [{
                    data: provinceData
                }]
            });
        });
        
        // 控制按钮功能
        document.getElementById('resetView').addEventListener('click', function() {
            myChart.dispatchAction({
                type: 'mapReset'
            });
        });
        
        document.getElementById('toggleLabels').addEventListener('click', function() {
            const current = myChart.getOption().series[0].emphasis.label.show;
            myChart.setOption({
                series: [{
                    emphasis: {
                        label: {
                            show: !current
                        }
                    }
                }]
            });
        });
        
        document.getElementById('zoomIn').addEventListener('click', function() {
            myChart.dispatchAction({
                type: 'mapZoom',
                zoom: 0.2
            });
        });
        
        document.getElementById('zoomOut').addEventListener('click', function() {
            myChart.dispatchAction({
                type: 'mapZoom',
                zoom: -0.2
            });
        });
        
        // 响应窗口大小变化
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    </script>
</body>
</html>